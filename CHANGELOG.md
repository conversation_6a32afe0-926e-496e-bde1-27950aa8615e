# Changelog

All notable changes to this project will be documented in this file. See [commit-and-tag-version](https://github.com/absolute-version/commit-and-tag-version) for commit guidelines.

## [1.1.0](https://github.com/njobnz/joplin-plugin-embedded-notes/compare/v1.0.1...v1.1.0) (2025-01-08)


### Features

* replace nested tokens ([80eeb87](https://github.com/njobnz/joplin-plugin-embedded-notes/commit/80eeb877b198c8518143e71e902b5720e5ec233a))


### Bug Fixes

* set app min version to 3.1 ([3dbcb77](https://github.com/njobnz/joplin-plugin-embedded-notes/commit/3dbcb779fbe926bf816c72172b7948157c84fc61))
* tokens not rendering after opening panels ([55bd434](https://github.com/njobnz/joplin-plugin-embedded-notes/commit/55bd4343e61d53f1d5fb0c07c2746aaf6f4677f1))

## [1.0.1](https://github.com/njobnz/joplin-plugin-embedded-notes/compare/v1.0.0...v1.0.1) (2025-01-08)


### Bug Fixes

* only parse content within fenced code blocks when enabled ([37e6af7](https://github.com/njobnz/joplin-plugin-embedded-notes/commit/37e6af7c88150753c4224392e13446c3f9d22cf4))
* replace content only within fenced code blocks when enabled ([f570b86](https://github.com/njobnz/joplin-plugin-embedded-notes/commit/f570b86033517cd3da818c30295b51cb8815de83))

## 1.0.0 (2025-01-07)
