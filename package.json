{"name": "joplin-plugin-embedded-notes", "version": "1.1.0", "description": "A plugin for Jo<PERSON>lin that enables referencing content from other notes using placeholder tokens.", "scripts": {"dist": "webpack --env joplin-plugin-config=buildMain && webpack --env joplin-plugin-config=buildExtraScripts && webpack --env joplin-plugin-config=createArchive", "prepare": "npm run dist", "release": "commit-and-tag-version", "updateVersion": "webpack --env joplin-plugin-config=updateVersion", "update": "npm install -g generator-joplin && yo joplin --node-package-manager npm --update --force", "test": "jest --passWithNoTests"}, "jest": {"transform": {".(ts|tsx)": "ts-jest"}, "testRegex": "(/__tests__/.*|\\.(test|spec))\\.(ts|tsx|js)$", "moduleFileExtensions": ["ts", "tsx", "js"], "moduleNameMapper": {"^api/types$": "<rootDir>/api/types"}}, "homepage": "https://github.com/njobnz/joplin-plugin-embedded-notes#readme", "repository": {"type": "git", "url": "**************:njobnz/joplin-plugin-embedded-notes.git"}, "bugs": {"url": "https://github.com/njobnz/joplin-plugin-embedded-notes/issues", "email": "njob<PERSON>@proton.me"}, "license": "MIT", "keywords": ["joplin-plugin"], "files": ["publish"], "devDependencies": {"@codemirror/autocomplete": "^6.18.3", "@codemirror/state": "^6.4.1", "@codemirror/view": "^6.35.2", "@jest/globals": "^29.7.0", "@types/jest": "^29.5.14", "@types/markdown-it": "^14.1.2", "@types/node": "^18.7.13", "chalk": "^4.1.0", "copy-webpack-plugin": "^11.0.0", "font-awesome-filetypes": "^2.1.0", "fs-extra": "^10.1.0", "glob": "^8.0.3", "jest": "^29.7.0", "jest-when": "^3.7.0", "tar": "^6.1.11", "ts-jest": "^29.2.5", "ts-loader": "^9.3.1", "typescript": "^4.8.2", "webpack": "^5.74.0", "webpack-cli": "^4.10.0"}, "dependencies": {"delegate": "^3.2.0", "markdown-it": "^14.1.0"}}