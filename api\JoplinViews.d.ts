import Plugin from '../Plugin';
import JoplinViewsDialogs from './Jo<PERSON>linViewsDialogs';
import Jo<PERSON>linViewsMenuItems from './JoplinViewsMenuItems';
import Jo<PERSON>linViewsMenus from './JoplinViewsMenus';
import JoplinViewsToolbarButtons from './JoplinViewsToolbarButtons';
import JoplinViewsPanels from './JoplinViewsPanels';
import <PERSON><PERSON><PERSON>ViewsNoteList from './JoplinViewsNoteList';
import Jo<PERSON>linViewsEditors from './JoplinViewsEditor';
/**
 * This namespace provides access to view-related services.
 *
 * All view services provide a `create()` method which you would use to create the view object, whether it's a dialog, a toolbar button or a menu item.
 * In some cases, the `create()` method will return a [[ViewHandle]], which you would use to act on the view, for example to set certain properties or call some methods.
 */
export default class JoplinViews {
    private store;
    private plugin;
    private panels_;
    private menuItems_;
    private menus_;
    private toolbarButtons_;
    private dialogs_;
    private editors_;
    private noteList_;
    private implementation_;
    constructor(implementation: any, plugin: Plugin, store: any);
    get dialogs(): JoplinViewsDialogs;
    get panels(): JoplinViewsPanels;
    get editors(): JoplinViewsEditors;
    get menuItems(): JoplinViewsMenuItems;
    get menus(): JoplinViewsMenus;
    get toolbarButtons(): JoplinViewsToolbarButtons;
    get noteList(): JoplinViewsNoteList;
}
